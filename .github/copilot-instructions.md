<!-- Use this file to provide workspace-specific custom instructions to <PERSON>pi<PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Next.js + Tailwind CSS + Supabase project for a university question paper browser. Use best practices for Next.js App Router, Supabase client, and role-based access control.
