# Deployment Guide for PYQFinder

This guide will help you deploy PYQFinder to production using Vercel and Supabase.

## Prerequisites

- GitHub account
- Vercel account
- Supabase account
- Domain name (optional)

## Step 1: Prepare Your Repository

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Ensure all files are included**
   - `supabase_schema.sql`
   - `supabase_rls_policies.sql`
   - `sample_data.sql` (optional)
   - `.env.example`

## Step 2: Set Up Production Supabase Project

1. **Create a new Supabase project**
   - Go to [supabase.com](https://supabase.com)
   - Click "New Project"
   - Choose a name like "pyqfinder-prod"
   - Select a region close to your users
   - Set a strong database password

2. **Run the database schema**
   - Go to SQL Editor in your Supabase dashboard
   - Copy and paste the contents of `supabase_schema.sql`
   - Click "Run"

3. **Set up RLS policies**
   - In the SQL Editor, run the contents of `supabase_rls_policies.sql`
   - Click "Run"

4. **Create storage bucket**
   - Go to Storage in your Supabase dashboard
   - Create a new bucket named `question-papers`
   - Make it public by going to bucket settings
   - Set up the following policies for the bucket:
     ```sql
     -- Allow authenticated users to upload files
     CREATE POLICY "Allow authenticated uploads" ON storage.objects
     FOR INSERT WITH CHECK (bucket_id = 'question-papers' AND auth.role() = 'authenticated');
     
     -- Allow public access to files
     CREATE POLICY "Allow public downloads" ON storage.objects
     FOR SELECT USING (bucket_id = 'question-papers');
     ```

5. **Get your credentials**
   - Go to Settings > API
   - Copy your Project URL and anon key
   - Copy your service role key (keep this secret!)

## Step 3: Deploy to Vercel

1. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Choose the repository you just pushed

2. **Configure environment variables**
   In the Vercel deployment settings, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
   ```

3. **Deploy**
   - Click "Deploy"
   - Wait for the build to complete
   - Your app will be available at a Vercel URL

## Step 4: Set Up Custom Domain (Optional)

1. **Add domain in Vercel**
   - Go to your project settings in Vercel
   - Click "Domains"
   - Add your custom domain

2. **Configure DNS**
   - Add a CNAME record pointing to your Vercel deployment
   - Or use Vercel's nameservers

## Step 5: Create Admin User

1. **Sign up through your app**
   - Go to your deployed app
   - Sign up with your admin email
   - Complete the registration process

2. **Update user role in Supabase**
   - Go to your Supabase dashboard
   - Navigate to Table Editor > profiles
   - Find your user and change the role to 'admin'

## Step 6: Add Sample Data (Optional)

1. **Run sample data script**
   - In Supabase SQL Editor
   - Run the contents of `sample_data.sql`
   - Update the user IDs and file URLs as needed

## Step 7: Configure Email (Optional)

1. **Set up SMTP in Supabase**
   - Go to Authentication > Settings
   - Configure SMTP settings for email confirmations
   - Or use Supabase's built-in email service

## Step 8: Set Up Monitoring

1. **Enable Vercel Analytics**
   - In your Vercel project settings
   - Enable Web Analytics
   - Enable Speed Insights

2. **Monitor Supabase**
   - Check the Supabase dashboard regularly
   - Monitor database usage and API calls
   - Set up alerts for high usage

## Environment Variables Reference

### Required
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (server-side only)

### Optional
- `NEXTAUTH_URL`: Your app's URL (auto-detected by Vercel)
- `NEXTAUTH_SECRET`: Random string for NextAuth (if using)
- `NEXT_PUBLIC_GA_ID`: Google Analytics ID
- `NEXT_PUBLIC_SENTRY_DSN`: Sentry error tracking

## Security Checklist

- [ ] RLS policies are enabled and tested
- [ ] Service role key is kept secret
- [ ] File upload limits are configured
- [ ] CORS is properly configured in Supabase
- [ ] Rate limiting is enabled (if needed)
- [ ] SSL/HTTPS is enabled (automatic with Vercel)

## Performance Optimization

1. **Database Indexes**
   - The schema includes necessary indexes
   - Monitor slow queries in Supabase

2. **Image Optimization**
   - Use Next.js Image component for any images
   - Optimize file sizes

3. **Caching**
   - Vercel automatically caches static assets
   - Consider adding API route caching if needed

## Backup Strategy

1. **Database Backups**
   - Supabase automatically backs up your database
   - Consider setting up additional backups for critical data

2. **File Backups**
   - Supabase Storage is replicated
   - Consider additional backup strategy for uploaded files

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check environment variables are set correctly
   - Ensure all dependencies are in package.json

2. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check if RLS policies are blocking access

3. **File Upload Issues**
   - Verify storage bucket is public
   - Check storage policies
   - Ensure file size limits are appropriate

4. **Authentication Issues**
   - Check Supabase Auth settings
   - Verify redirect URLs are configured

### Getting Help

- Check Vercel deployment logs
- Monitor Supabase logs and metrics
- Use browser developer tools for client-side issues
- Check the GitHub repository for updates

## Maintenance

1. **Regular Updates**
   - Keep dependencies updated
   - Monitor security advisories
   - Update Node.js version as needed

2. **Database Maintenance**
   - Monitor database size and performance
   - Clean up old data if necessary
   - Optimize queries based on usage patterns

3. **Content Moderation**
   - Regularly review uploaded content
   - Implement automated content scanning if needed
   - Monitor for inappropriate uploads

---

Your PYQFinder application should now be successfully deployed and ready for production use!
