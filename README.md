# PYQFinder - University Question Papers Portal

A comprehensive full-stack web application for browsing, searching, and downloading previous year question papers from universities. Built with Next.js, Tailwind CSS, and Supabase.

## 🚀 Features

### For Students
- **Smart Search**: Find papers by university, course, subject, year, semester, or keywords
- **Advanced Filtering**: Multiple filter options for precise results
- **Instant Download**: Download PDF files with one click
- **User Authentication**: Secure signup/login with role-based access
- **Responsive Design**: Works perfectly on desktop and mobile devices

### For Admins/Uploaders
- **Upload Management**: Easy-to-use upload form with metadata
- **Approval System**: Admin approval workflow for uploaded papers
- **Dashboard**: Comprehensive admin dashboard with statistics
- **User Management**: Role-based user management system
- **Download Analytics**: Track download counts and popular papers

### Technical Features
- **Role-Based Access Control**: Student, Uploader, and Admin roles
- **File Validation**: PDF-only uploads with size limits
- **Search Optimization**: Full-text search with PostgreSQL
- **Download Tracking**: Automatic download count increment
- **Secure Storage**: Supabase Storage for file management
- **Row Level Security**: Database-level security policies

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Authentication**: Supabase Auth with RLS
- **File Storage**: Supabase Storage
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account
- Git

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/pyqfinder.git
cd pyqfinder
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to Settings > Database and run the SQL from `supabase_schema.sql`
4. Run the RLS policies from `supabase_rls_policies.sql`
5. Create a storage bucket named `question-papers` and make it public

### 4. Environment Variables

Copy `.env.example` to `.env.local` and fill in your Supabase credentials:

```bash
cp .env.example .env.local
```

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 5. Run the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── api/               # API routes
│   ├── browse/            # Browse papers page
│   ├── dashboard/         # Admin dashboard
│   ├── upload/            # Upload page
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── AuthForm.tsx       # Authentication form
│   ├── DashboardContent.tsx # Dashboard content
│   ├── QuestionBrowser.tsx  # Browse/search interface
│   └── UploadPanel.tsx    # Upload form
├── middleware.ts          # Route protection
└── supabase.ts           # Supabase client
```

## 🗄️ Database Schema

The application uses the following main tables:

- **universities**: University information
- **courses**: Courses offered by universities
- **subjects**: Subjects within courses
- **profiles**: User profiles with roles
- **question_papers**: Question paper metadata and files
- **download_logs**: Download tracking for analytics

See `supabase_schema.sql` for the complete database schema.

## 🔐 Authentication & Authorization

### User Roles

1. **Student** (default): Can browse and download papers
2. **Uploader**: Can upload papers + student permissions
3. **Admin**: Full access including user management and approvals

### Route Protection

- `/browse`: Public access
- `/upload`: Requires uploader or admin role
- `/dashboard`: Requires uploader or admin role
- `/admin`: Requires admin role only

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/pyqfinder)

### Environment Variables for Production

Make sure to set these in your deployment platform:

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
```

## 📝 Usage Guide

### For Students

1. **Sign Up**: Create an account with your university and course details
2. **Browse**: Use the search and filter options to find papers
3. **Download**: Click download to get PDF files instantly

### For Uploaders

1. **Upload Papers**: Use the upload form to add new question papers
2. **Add Metadata**: Include title, subject, year, semester, and keywords
3. **Wait for Approval**: Admin will review and approve uploads

### For Admins

1. **Dashboard**: Monitor uploads, downloads, and user activity
2. **Approve Papers**: Review and approve/reject uploaded papers
3. **Manage Users**: View user statistics and manage roles

## 🔧 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs and request features via GitHub Issues

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Supabase](https://supabase.com/) for the backend infrastructure
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Vercel](https://vercel.com/) for seamless deployment

---

**Built with ❤️ for students by students**
