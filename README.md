# PYQFinder

A full-stack web application for browsing, searching, and downloading previous year question papers (PYQs) for university students.

## Tech Stack
- **Frontend:** Next.js 14 (App Router, TypeScript)
- **Styling:** Tailwind CSS
- **Backend:** Supabase (Database, Auth, Storage)

## Features
- Auth roles: student, admin, uploader
- Browse, search, and download question papers
- Admin/uploader can upload PDFs and add metadata
- Role-based access and page protection

## Setup

1. **Clone this repo and install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   Create a `.env.local` file in the root:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

3. **Supabase Setup:**
   - Create a Supabase project at https://app.supabase.com
   - Run the SQL schema below in the Supabase SQL editor.
   - Set up Storage bucket (e.g., `question-papers`)
   - Enable Row Level Security (RLS) and add policies (see below)

4. **Run the app locally:**
   ```bash
   npm run dev
   ```

## Supabase SQL Schema

```
-- Table: universities
create table universities (
  id uuid primary key default gen_random_uuid(),
  name text not null unique
);

-- Table: courses
create table courses (
  id uuid primary key default gen_random_uuid(),
  university_id uuid references universities(id) on delete cascade,
  name text not null,
  code text,
  unique (university_id, name)
);

-- Table: subjects
create table subjects (
  id uuid primary key default gen_random_uuid(),
  course_id uuid references courses(id) on delete cascade,
  name text not null,
  code text,
  semester int,
  unique (course_id, name, semester)
);

-- Table: profiles
create table profiles (
  id uuid primary key, -- matches auth.users.id
  full_name text,
  email text unique,
  role text check (role in ('student', 'admin', 'uploader')) not null default 'student',
  university_id uuid references universities(id)
);

-- Table: question_papers
create table question_papers (
  id uuid primary key default gen_random_uuid(),
  subject_id uuid references subjects(id) on delete cascade,
  year int not null,
  semester int not null,
  file_url text not null,
  uploaded_by uuid references profiles(id),
  created_at timestamp with time zone default timezone('utc'::text, now()),
  unique (subject_id, year, semester)
);
```

## RLS Policies

- Only admin/uploader can insert into question_papers
- Any student can read from question_papers
- Profiles can only be updated by the owner

## Deploy
- Deploy to Vercel for best Next.js experience: https://vercel.com/import

---

Feel free to continue building features!
