{"name": "pyqfinder", "version": "1.0.0", "description": "University Question Papers Portal - Browse, search, and download previous year question papers", "private": true, "keywords": ["question papers", "university", "education", "nextjs", "supabase", "tailwindcss"], "author": "PYQFinder Team", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist", "postinstall": "husky install || true"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.50.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "lucide-react": "^0.263.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/pyqfinder.git"}, "bugs": {"url": "https://github.com/your-username/pyqfinder/issues"}, "homepage": "https://pyqfinder.vercel.app"}