-- Sample data for PYQFinder
-- Run this after setting up the main schema

-- Insert sample universities
INSERT INTO universities (id, name, short_name, location) VALUES
  ('550e8400-e29b-41d4-a716-************', 'Indian Institute of Technology Delhi', 'IIT Delhi', 'New Delhi'),
  ('550e8400-e29b-41d4-a716-************', 'University of Mumbai', 'Mumbai University', 'Mumbai'),
  ('550e8400-e29b-41d4-a716-************', 'Delhi University', 'DU', 'New Delhi'),
  ('550e8400-e29b-41d4-a716-************', 'Indian Institute of Science', 'IISc', 'Bangalore'),
  ('550e8400-e29b-41d4-a716-************', 'Jawaharlal Nehru University', 'JNU', 'New Delhi');

-- Insert sample courses
INSERT INTO courses (id, university_id, name, code, duration_years, degree_type) VALUES
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Computer Science and Engineering', 'CSE', 4, 'undergraduate'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Electrical Engineering', 'EE', 4, 'undergraduate'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Information Technology', 'IT', 4, 'undergraduate'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Bachelor of Commerce', 'B.Com', 3, 'undergraduate'),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Master of Computer Applications', 'MCA', 2, 'postgraduate');

-- Insert sample subjects
INSERT INTO subjects (id, course_id, name, code, semester, credits, is_elective) VALUES
  -- CSE subjects
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Data Structures and Algorithms', 'CSE201', 3, 4, false),
  ('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-************', 'Database Management Systems', 'CSE301', 5, 4, false),
  ('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-************', 'Operating Systems', 'CSE302', 5, 4, false),
  ('550e8400-e29b-41d4-a716-446655440024', '550e8400-e29b-41d4-a716-************', 'Computer Networks', 'CSE401', 7, 4, false),
  ('550e8400-e29b-41d4-a716-446655440025', '550e8400-e29b-41d4-a716-************', 'Machine Learning', 'CSE402', 7, 3, true),
  
  -- EE subjects
  ('550e8400-e29b-41d4-a716-446655440026', '550e8400-e29b-41d4-a716-************', 'Circuit Analysis', 'EE201', 3, 4, false),
  ('550e8400-e29b-41d4-a716-446655440027', '550e8400-e29b-41d4-a716-************', 'Digital Electronics', 'EE301', 5, 4, false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Power Systems', 'EE401', 7, 4, false),
  
  -- IT subjects
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Web Development', 'IT301', 5, 3, false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Software Engineering', 'IT401', 7, 4, false),
  
  -- Commerce subjects
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Financial Accounting', 'COM101', 1, 4, false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Business Statistics', 'COM201', 3, 3, false),
  
  -- MCA subjects
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Advanced Database Systems', 'MCA201', 2, 4, false),
  ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Artificial Intelligence', 'MCA301', 3, 4, false);

-- Insert sample admin profile (you'll need to create this user in Supabase Auth first)
-- Replace the UUID with the actual user ID from Supabase Auth
INSERT INTO profiles (id, full_name, email, role, university_id, course_id, current_semester, is_active) VALUES
  ('********-0000-0000-0000-********0001', 'Admin User', '<EMAIL>', 'admin', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 8, true);

-- Insert sample question papers (these will need actual file URLs from Supabase Storage)
INSERT INTO question_papers (
  id, title, subject_id, university_id, course_id, year, semester, exam_type, 
  file_url, file_name, file_size, keywords, description, download_count, 
  is_approved, uploaded_by
) VALUES
  (
    '550e8400-e29b-41d4-a716-************',
    'Data Structures and Algorithms - End Term Examination',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    2023,
    3,
    'end_term',
    'https://your-supabase-storage-url/question-papers/sample1.pdf',
    'DSA_EndTerm_2023.pdf',
    1024000,
    ARRAY['data structures', 'algorithms', 'trees', 'graphs', 'sorting'],
    'Comprehensive end term examination covering all major data structures and algorithms',
    45,
    true,
    '********-0000-0000-0000-********0001'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440042',
    'Database Management Systems - Mid Term',
    '550e8400-e29b-41d4-a716-446655440022',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    2023,
    5,
    'mid_term',
    'https://your-supabase-storage-url/question-papers/sample2.pdf',
    'DBMS_MidTerm_2023.pdf',
    856000,
    ARRAY['database', 'sql', 'normalization', 'er model'],
    'Mid term examination focusing on database design and SQL queries',
    32,
    true,
    '********-0000-0000-0000-********0001'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440043',
    'Operating Systems - End Term Examination',
    '550e8400-e29b-41d4-a716-446655440023',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    2022,
    5,
    'end_term',
    'https://your-supabase-storage-url/question-papers/sample3.pdf',
    'OS_EndTerm_2022.pdf',
    1200000,
    ARRAY['operating systems', 'processes', 'threads', 'memory management', 'scheduling'],
    'Final examination covering process management, memory management, and file systems',
    67,
    true,
    '********-0000-0000-0000-********0001'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440044',
    'Machine Learning - Assignment',
    '550e8400-e29b-41d4-a716-446655440025',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    2023,
    7,
    'assignment',
    'https://your-supabase-storage-url/question-papers/sample4.pdf',
    'ML_Assignment_2023.pdf',
    750000,
    ARRAY['machine learning', 'neural networks', 'classification', 'regression'],
    'Programming assignment on implementing basic ML algorithms',
    28,
    true,
    '********-0000-0000-0000-********0001'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440045',
    'Circuit Analysis - Quiz',
    '550e8400-e29b-41d4-a716-446655440026',
    '550e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    2023,
    3,
    'quiz',
    'https://your-supabase-storage-url/question-papers/sample5.pdf',
    'CircuitAnalysis_Quiz_2023.pdf',
    450000,
    ARRAY['circuits', 'analysis', 'kirchhoff', 'ohm law'],
    'Quick quiz on basic circuit analysis principles',
    15,
    true,
    '********-0000-0000-0000-********0001'
  );

-- Insert some sample download logs
INSERT INTO download_logs (paper_id, user_id, downloaded_at) VALUES
  ('550e8400-e29b-41d4-a716-************', '********-0000-0000-0000-********0001', NOW() - INTERVAL '1 day'),
  ('550e8400-e29b-41d4-a716-446655440042', '********-0000-0000-0000-********0001', NOW() - INTERVAL '2 days'),
  ('550e8400-e29b-41d4-a716-446655440043', '********-0000-0000-0000-********0001', NOW() - INTERVAL '3 days');

-- Note: Remember to update the file URLs with actual Supabase Storage URLs
-- and replace the user IDs with real user IDs from your Supabase Auth users
