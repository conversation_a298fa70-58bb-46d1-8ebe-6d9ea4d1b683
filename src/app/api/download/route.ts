import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { paperId } = await request.json();

    if (!paperId) {
      return NextResponse.json(
        { error: 'Paper ID is required' },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user (optional - downloads can be tracked for anonymous users too)
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // Increment download count using the database function
    const { error: incrementError } = await supabase.rpc(
      'increment_download_count',
      { paper_id: paperId }
    );

    if (incrementError) {
      console.error('Error incrementing download count:', incrementError);
      return NextResponse.json(
        { error: 'Failed to track download' },
        { status: 500 }
      );
    }

    // Optionally, log the download for analytics
    if (user) {
      const { error: logError } = await supabase
        .from('download_logs')
        .insert([{
          paper_id: paperId,
          user_id: user.id,
          downloaded_at: new Date().toISOString()
        }]);

      // Don't fail the request if logging fails
      if (logError) {
        console.error('Error logging download:', logError);
      }
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Download tracking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve download statistics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paperId = searchParams.get('paperId');

    const supabase = createRouteHandlerClient({ cookies });

    if (paperId) {
      // Get download count for specific paper
      const { data, error } = await supabase
        .from('question_papers')
        .select('download_count')
        .eq('id', paperId)
        .single();

      if (error) {
        return NextResponse.json(
          { error: 'Paper not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ download_count: data.download_count });
    } else {
      // Get top downloaded papers
      const { data, error } = await supabase
        .from('question_papers')
        .select(`
          id,
          title,
          download_count,
          subjects(name),
          universities(name)
        `)
        .eq('is_approved', true)
        .order('download_count', { ascending: false })
        .limit(10);

      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch statistics' },
          { status: 500 }
        );
      }

      return NextResponse.json({ top_downloads: data });
    }

  } catch (error) {
    console.error('Download stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
