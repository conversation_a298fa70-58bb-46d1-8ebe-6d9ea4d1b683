import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "PYQFinder - University Question Papers Portal",
  description: "Find and download previous year question papers from universities across the country. Search by university, course, subject, and year.",
  keywords: ["question papers", "previous year", "university", "exam", "study material", "PYQ"],
  authors: [{ name: "PYQFinder Team" }],
  openGraph: {
    title: "PYQFinder - University Question Papers Portal",
    description: "Find and download previous year question papers from universities across the country.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "PYQFinder - University Question Papers Portal",
    description: "Find and download previous year question papers from universities across the country.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-gray-50">
        {children}
      </body>
    </html>
  );
}
