"use client";
import { useState, useEffect } from "react";
import { supabase } from "../supabase";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface AuthFormProps {
  mode: "login" | "signup";
}

interface University {
  id: string;
  name: string;
}

interface Course {
  id: string;
  name: string;
  code?: string;
}

export default function AuthForm({ mode }: AuthFormProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [universityId, setUniversityId] = useState("");
  const [courseId, setCourseId] = useState("");
  const [currentSemester, setCurrentSemester] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const [universities, setUniversities] = useState<University[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (mode === "signup") {
      loadUniversities();
    }
  }, [mode]);

  useEffect(() => {
    if (universityId) {
      loadCourses(universityId);
      setCourseId("");
    }
  }, [universityId]);

  const loadUniversities = async () => {
    const { data, error } = await supabase
      .from("universities")
      .select("id, name")
      .order("name");

    if (error) {
      console.error("Error loading universities:", error);
    } else {
      setUniversities(data || []);
    }
  };

  const loadCourses = async (universityId: string) => {
    const { data, error } = await supabase
      .from("courses")
      .select("id, name, code")
      .eq("university_id", universityId)
      .order("name");

    if (error) {
      console.error("Error loading courses:", error);
    } else {
      setCourses(data || []);
    }
  };

  const validateForm = (): string | null => {
    if (mode === "signup") {
      if (!fullName.trim()) return "Full name is required";
      if (!universityId) return "Please select a university";
      if (!courseId) return "Please select a course";
      if (!currentSemester) return "Please select your current semester";
      if (password.length < 6) return "Password must be at least 6 characters";
      if (password !== confirmPassword) return "Passwords do not match";
    }
    if (!email.includes("@")) return "Please enter a valid email";
    if (!password) return "Password is required";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    try {
      if (mode === "signup") {
        // Sign up the user
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
              university_id: universityId,
              course_id: courseId,
              current_semester: parseInt(currentSemester),
              phone: phone || null
            }
          }
        });

        if (authError) throw authError;

        if (authData.user) {
          // Create profile record
          const { error: profileError } = await supabase
            .from("profiles")
            .insert([{
              id: authData.user.id,
              full_name: fullName,
              email: email,
              role: "student", // Default role
              university_id: universityId,
              course_id: courseId,
              current_semester: parseInt(currentSemester),
              phone: phone || null
            }]);

          if (profileError) {
            console.error("Error creating profile:", profileError);
            // Don't throw here as the user is already created
          }
        }

        setSuccess("Account created successfully! Please check your email for a confirmation link.");
        // Clear form
        setEmail("");
        setPassword("");
        setConfirmPassword("");
        setFullName("");
        setUniversityId("");
        setCourseId("");
        setCurrentSemester("");
        setPhone("");

        // Redirect to login after a delay
        setTimeout(() => {
          router.push("/auth/login");
        }, 3000);

      } else {
        // Sign in the user
        const { data, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password
        });

        if (signInError) throw signInError;

        if (data.user) {
          // Get user profile to determine redirect
          const { data: profile, error: profileError } = await supabase
            .from("profiles")
            .select("role")
            .eq("id", data.user.id)
            .single();

          if (profileError) {
            console.error("Error fetching profile:", profileError);
            router.push("/"); // Default redirect
          } else {
            // Role-based redirect
            if (profile.role === "admin" || profile.role === "uploader") {
              router.push("/dashboard");
            } else {
              router.push("/browse");
            }
          }
        }
      }
    } catch (err: any) {
      setError(err.message || "An error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            {mode === "signup" ? "Join PYQFinder" : "Welcome Back"}
          </h2>
          <p className="text-gray-600">
            {mode === "signup"
              ? "Create your account to access thousands of question papers"
              : "Sign in to your account to continue"
            }
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Email Address *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter your email address"
                  required
                />
              </div>
            </div>

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Password *
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter your password"
            required
            minLength={6}
          />
          {mode === "signup" && (
            <p className="text-xs text-gray-500 mt-1">
              Password must be at least 6 characters long
            </p>
          )}
        </div>

        {/* Confirm Password (Signup only) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Confirm Password *
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Confirm your password"
              required
            />
          </div>
        )}

        {/* Full Name (Signup only) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter your full name"
              required
            />
          </div>
        )}

        {/* University (Signup only) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              University *
            </label>
            <select
              value={universityId}
              onChange={(e) => setUniversityId(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select your university</option>
              {universities.map((university) => (
                <option key={university.id} value={university.id}>
                  {university.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Course (Signup only) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Course *
            </label>
            <select
              value={courseId}
              onChange={(e) => setCourseId(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={!universityId}
            >
              <option value="">Select your course</option>
              {courses.map((course) => (
                <option key={course.id} value={course.id}>
                  {course.name} {course.code && `(${course.code})`}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Current Semester (Signup only) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Current Semester *
            </label>
            <select
              value={currentSemester}
              onChange={(e) => setCurrentSemester(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select your current semester</option>
              {Array.from({ length: 8 }, (_, i) => i + 1).map((sem) => (
                <option key={sem} value={sem}>
                  Semester {sem}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Phone (Signup only, optional) */}
        {mode === "signup" && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number (Optional)
            </label>
            <input
              type="tel"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter your phone number"
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-800">{success}</p>
          </div>
        )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                loading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              } text-white`}
            >
              {loading
                ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
                    Processing...
                  </div>
                )
                : mode === "signup"
                  ? "Create Account"
                  : "Sign In"
              }
            </button>

            {/* Switch Mode Link */}
            <div className="text-center text-gray-600">
              {mode === "signup" ? (
                <>
                  Already have an account?{" "}
                  <Link href="/login" className="text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                    Sign in here
                  </Link>
                </>
              ) : (
                <>
                  Don't have an account?{" "}
                  <Link href="/signup" className="text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                    Create one here
                  </Link>
                </>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
