"use client";
import { useState, useEffect } from "react";
import { supabase } from "../supabase";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface User {
  id: string;
  email?: string;
}

interface Profile {
  id: string;
  full_name?: string;
  email?: string;
  role: string;
  university_id?: string;
  course_id?: string;
}

interface DashboardStats {
  totalPapers: number;
  pendingApproval: number;
  totalDownloads: number;
  totalUsers: number;
  myUploads: number;
}

interface QuestionPaper {
  id: string;
  title: string;
  year: number;
  semester: number;
  exam_type: string;
  download_count: number;
  is_approved: boolean;
  created_at: string;
  subject: { name: string };
  university: { name: string };
  course: { name: string };
}

export default function DashboardContent({ user, profile }: { user: User; profile: Profile }) {
  const [stats, setStats] = useState<DashboardStats>({
    totalPapers: 0,
    pendingApproval: 0,
    totalDownloads: 0,
    totalUsers: 0,
    myUploads: 0
  });
  const [recentPapers, setRecentPapers] = useState<QuestionPaper[]>([]);
  const [pendingPapers, setPendingPapers] = useState<QuestionPaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"overview" | "pending" | "recent">("overview");
  const router = useRouter();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadStats(),
        loadRecentPapers(),
        loadPendingPapers()
      ]);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Get total papers
      const { count: totalPapers } = await supabase
        .from("question_papers")
        .select("*", { count: "exact", head: true });

      // Get pending approval count
      const { count: pendingApproval } = await supabase
        .from("question_papers")
        .select("*", { count: "exact", head: true })
        .eq("is_approved", false);

      // Get total downloads
      const { data: downloadData } = await supabase
        .from("question_papers")
        .select("download_count");
      
      const totalDownloads = downloadData?.reduce((sum, paper) => sum + paper.download_count, 0) || 0;

      // Get total users (admin only)
      let totalUsers = 0;
      if (profile.role === "admin") {
        const { count } = await supabase
          .from("profiles")
          .select("*", { count: "exact", head: true });
        totalUsers = count || 0;
      }

      // Get my uploads
      const { count: myUploads } = await supabase
        .from("question_papers")
        .select("*", { count: "exact", head: true })
        .eq("uploaded_by", user.id);

      setStats({
        totalPapers: totalPapers || 0,
        pendingApproval: pendingApproval || 0,
        totalDownloads,
        totalUsers,
        myUploads: myUploads || 0
      });
    } catch (error) {
      console.error("Error loading stats:", error);
    }
  };

  const loadRecentPapers = async () => {
    try {
      const { data, error } = await supabase
        .from("question_papers")
        .select(`
          id,
          title,
          year,
          semester,
          exam_type,
          download_count,
          is_approved,
          created_at,
          subject:subjects(name),
          university:universities(name),
          course:courses(name)
        `)
        .eq("uploaded_by", user.id)
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) throw error;
      setRecentPapers(data || []);
    } catch (error) {
      console.error("Error loading recent papers:", error);
    }
  };

  const loadPendingPapers = async () => {
    try {
      const { data, error } = await supabase
        .from("question_papers")
        .select(`
          id,
          title,
          year,
          semester,
          exam_type,
          download_count,
          is_approved,
          created_at,
          subject:subjects(name),
          university:universities(name),
          course:courses(name)
        `)
        .eq("is_approved", false)
        .order("created_at", { ascending: false })
        .limit(10);

      if (error) throw error;
      setPendingPapers(data || []);
    } catch (error) {
      console.error("Error loading pending papers:", error);
    }
  };

  const handleApprove = async (paperId: string) => {
    try {
      const { error } = await supabase
        .from("question_papers")
        .update({
          is_approved: true,
          approved_by: user.id,
          approved_at: new Date().toISOString()
        })
        .eq("id", paperId);

      if (error) throw error;

      // Refresh data
      await loadDashboardData();
    } catch (error) {
      console.error("Error approving paper:", error);
      alert("Error approving paper. Please try again.");
    }
  };

  const handleReject = async (paperId: string) => {
    if (!confirm("Are you sure you want to reject this paper? This action cannot be undone.")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("question_papers")
        .delete()
        .eq("id", paperId);

      if (error) throw error;

      // Refresh data
      await loadDashboardData();
    } catch (error) {
      console.error("Error rejecting paper:", error);
      alert("Error rejecting paper. Please try again.");
    }
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/auth/login");
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-600">
                Welcome back, {profile.full_name || user.email}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                {profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}
              </span>
              <Link
                href="/upload"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Upload Paper
              </Link>
              <button
                onClick={handleSignOut}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Papers</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalPapers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingApproval}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Downloads</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalDownloads}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">My Uploads</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.myUploads}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab("overview")}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "overview"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Recent Uploads
              </button>
              {profile.role === "admin" && (
                <button
                  onClick={() => setActiveTab("pending")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "pending"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  Pending Approval ({stats.pendingApproval})
                </button>
              )}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === "overview" && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Your Recent Uploads</h3>
                {recentPapers.length === 0 ? (
                  <p className="text-gray-500">No uploads yet. <Link href="/upload" className="text-blue-600 hover:text-blue-700">Upload your first paper</Link></p>
                ) : (
                  <div className="space-y-4">
                    {recentPapers.map((paper) => (
                      <div key={paper.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-gray-900">{paper.title}</h4>
                            <p className="text-sm text-gray-600">
                              {paper.subject?.name} • {paper.year} • Semester {paper.semester}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(paper.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              paper.is_approved 
                                ? "bg-green-100 text-green-800" 
                                : "bg-yellow-100 text-yellow-800"
                            }`}>
                              {paper.is_approved ? "Approved" : "Pending"}
                            </span>
                            <span className="text-sm text-gray-500">
                              {paper.download_count} downloads
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === "pending" && profile.role === "admin" && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Papers Pending Approval</h3>
                {pendingPapers.length === 0 ? (
                  <p className="text-gray-500">No papers pending approval.</p>
                ) : (
                  <div className="space-y-4">
                    {pendingPapers.map((paper) => (
                      <div key={paper.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{paper.title}</h4>
                            <p className="text-sm text-gray-600">
                              {paper.university?.name} • {paper.course?.name} • {paper.subject?.name}
                            </p>
                            <p className="text-sm text-gray-600">
                              {paper.year} • Semester {paper.semester} • {paper.exam_type.replace("_", " ")}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              Uploaded on {new Date(paper.created_at).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex space-x-2 ml-4">
                            <button
                              onClick={() => handleApprove(paper.id)}
                              className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                            >
                              Approve
                            </button>
                            <button
                              onClick={() => handleReject(paper.id)}
                              className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                            >
                              Reject
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
