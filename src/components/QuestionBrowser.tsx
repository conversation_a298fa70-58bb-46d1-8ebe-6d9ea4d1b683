"use client";
import { useEffect, useState, useCallback } from "react";
import { supabase } from "../supabase";

interface University {
  id: string;
  name: string;
  short_name?: string;
}

interface Course {
  id: string;
  name: string;
  code?: string;
}

interface Subject {
  id: string;
  name: string;
  code?: string;
  semester: number;
}

interface QuestionPaper {
  id: string;
  title: string;
  year: number;
  semester: number;
  exam_type: string;
  file_url: string;
  file_name: string;
  file_size?: number;
  keywords?: string[];
  description?: string;
  download_count: number;
  created_at: string;
  university: { name: string };
  course: { name: string; code?: string };
  subject: { name: string; code?: string };
}

interface Filters {
  university: string;
  course: string;
  subject: string;
  semester: string;
  year: string;
  examType: string;
}

export default function QuestionBrowser() {
  const [universities, setUniversities] = useState<University[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [papers, setPapers] = useState<QuestionPaper[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<Filters>({
    university: "",
    course: "",
    subject: "",
    semester: "",
    year: "",
    examType: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const itemsPerPage = 10;

  // Load universities on component mount
  useEffect(() => {
    loadUniversities();
  }, []);

  // Load courses when university changes
  useEffect(() => {
    if (filters.university) {
      loadCourses(filters.university);
      setFilters(prev => ({ ...prev, course: "", subject: "" }));
    } else {
      setCourses([]);
      setSubjects([]);
    }
  }, [filters.university]);

  // Load subjects when course changes
  useEffect(() => {
    if (filters.course) {
      loadSubjects(filters.course);
      setFilters(prev => ({ ...prev, subject: "" }));
    } else {
      setSubjects([]);
    }
  }, [filters.course]);

  // Search papers when filters or search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchPapers();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [filters, searchTerm, currentPage]);

  const loadUniversities = async () => {
    const { data, error } = await supabase
      .from("universities")
      .select("id, name, short_name")
      .order("name");

    if (error) {
      console.error("Error loading universities:", error);
    } else {
      setUniversities(data || []);
    }
  };

  const loadCourses = async (universityId: string) => {
    const { data, error } = await supabase
      .from("courses")
      .select("id, name, code")
      .eq("university_id", universityId)
      .order("name");

    if (error) {
      console.error("Error loading courses:", error);
    } else {
      setCourses(data || []);
    }
  };

  const loadSubjects = async (courseId: string) => {
    const { data, error } = await supabase
      .from("subjects")
      .select("id, name, code, semester")
      .eq("course_id", courseId)
      .order("semester, name");

    if (error) {
      console.error("Error loading subjects:", error);
    } else {
      setSubjects(data || []);
    }
  };

  const searchPapers = async () => {
    setLoading(true);
    try {
      // Use the search function we created in the schema
      const { data, error } = await supabase.rpc('search_question_papers', {
        search_term: searchTerm,
        university_filter: filters.university || null,
        course_filter: filters.course || null,
        subject_filter: filters.subject || null,
        year_filter: filters.year ? parseInt(filters.year) : null,
        semester_filter: filters.semester ? parseInt(filters.semester) : null,
        exam_type_filter: filters.examType || null
      });

      if (error) {
        console.error("Error searching papers:", error);
        setPapers([]);
      } else {
        // Transform the data to match our interface
        const transformedPapers = (data || []).map((paper: any) => ({
          id: paper.id,
          title: paper.title,
          year: paper.year,
          semester: paper.semester,
          exam_type: paper.exam_type,
          file_url: paper.file_url,
          file_name: paper.file_name,
          download_count: paper.download_count,
          created_at: paper.created_at,
          university: { name: paper.university_name },
          course: { name: paper.course_name },
          subject: { name: paper.subject_name }
        }));

        // Apply pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        setPapers(transformedPapers.slice(startIndex, endIndex));
        setTotalCount(transformedPapers.length);
      }
    } catch (error) {
      console.error("Error in search:", error);
      setPapers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (paperId: string, fileUrl: string) => {
    try {
      // Increment download count
      await supabase.rpc('increment_download_count', { paper_id: paperId });

      // Open file in new tab
      window.open(fileUrl, '_blank');

      // Update local state
      setPapers(prev => prev.map(paper =>
        paper.id === paperId
          ? { ...paper, download_count: paper.download_count + 1 }
          : paper
      ));
    } catch (error) {
      console.error("Error tracking download:", error);
      // Still allow download even if tracking fails
      window.open(fileUrl, '_blank');
    }
  };

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setFilters({
      university: "",
      course: "",
      subject: "",
      semester: "",
      year: "",
      examType: ""
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  const examTypes = [
    { value: "", label: "All Exam Types" },
    { value: "mid_term", label: "Mid Term" },
    { value: "end_term", label: "End Term" },
    { value: "quiz", label: "Quiz" },
    { value: "assignment", label: "Assignment" },
    { value: "practical", label: "Practical" },
    { value: "other", label: "Other" }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 25 }, (_, i) => currentYear - i);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Browse <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Question Papers</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover thousands of previous year question papers from top universities. Use our advanced filters to find exactly what you need.
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Search & Filter</h2>
                <p className="text-gray-600">Find the perfect question papers for your studies</p>
              </div>
              <button
                onClick={clearFilters}
                className="px-6 py-2.5 text-sm bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium"
              >
                Clear All Filters
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="p-6">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search by title, subject, university, or keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg placeholder-gray-400 bg-gray-50 focus:bg-white transition-all duration-200"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="p-6 border-t border-gray-100 bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <select
                value={filters.university}
                onChange={(e) => handleFilterChange("university", e.target.value)}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <option value="">All Universities</option>
                {universities.map((university) => (
                  <option key={university.id} value={university.id}>
                    {university.short_name || university.name}
                  </option>
                ))}
              </select>

              <select
                value={filters.course}
                onChange={(e) => handleFilterChange("course", e.target.value)}
                disabled={!filters.university}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="">All Courses</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.name} {course.code && `(${course.code})`}
                  </option>
                ))}
              </select>

              <select
                value={filters.subject}
                onChange={(e) => handleFilterChange("subject", e.target.value)}
                disabled={!filters.course}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                <option value="">All Subjects</option>
                {subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name} {subject.code && `(${subject.code})`}
                  </option>
                ))}
              </select>

              <select
                value={filters.year}
                onChange={(e) => handleFilterChange("year", e.target.value)}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <option value="">All Years</option>
                {years.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>

              <select
                value={filters.semester}
                onChange={(e) => handleFilterChange("semester", e.target.value)}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <option value="">All Semesters</option>
                {Array.from({ length: 8 }, (_, i) => i + 1).map((sem) => (
                  <option key={sem} value={sem}>
                    Semester {sem}
                  </option>
                ))}
              </select>

              <select
                value={filters.examType}
                onChange={(e) => handleFilterChange("examType", e.target.value)}
                className="p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                {examTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center py-16">
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600"></div>
                <span className="mt-4 text-gray-600 font-medium">Searching for papers...</span>
              </div>
            </div>
          )}

          {/* Results */}
          {!loading && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="text-gray-600">
                  <span className="font-semibold text-gray-900">{totalCount}</span> question papers found
                  {totalPages > 1 && (
                    <span className="ml-2 text-sm">
                      (Page {currentPage} of {totalPages})
                    </span>
                  )}
                </div>
              </div>

              <div className="space-y-6">
                {papers.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No question papers found</h3>
                    <p className="text-gray-600 mb-6">Try adjusting your search criteria or filters to find what you're looking for.</p>
                    <button
                      onClick={clearFilters}
                      className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium"
                    >
                      Clear All Filters
                    </button>
                  </div>
                ) : (
                  papers.map((paper) => (
                    <div
                      key={paper.id}
                      className="group bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-xl hover:border-blue-200 transition-all duration-300"
                    >
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-4">
                            <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                              {paper.title}
                            </h3>
                            <span className="ml-4 px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                              {paper.exam_type.replace("_", " ").replace(/\b\w/g, l => l.toUpperCase())}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm mb-4">
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                              </svg>
                              <span className="text-gray-600">{paper.university.name}</span>
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                              </svg>
                              <span className="text-gray-600">{paper.course.name}</span>
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                              </svg>
                              <span className="text-gray-600">{paper.subject.name}</span>
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span className="text-gray-600">{paper.year} • Semester {paper.semester}</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span>Uploaded on {new Date(paper.created_at).toLocaleDateString()}</span>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <span>{paper.download_count} downloads</span>
                            </div>
                          </div>
                        </div>

                        <div className="mt-6 lg:mt-0 lg:ml-8">
                          <button
                            onClick={() => handleDownload(paper.id, paper.file_url)}
                            className="group w-full lg:w-auto px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl font-semibold flex items-center justify-center"
                          >
                            <svg className="w-5 h-5 mr-2 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Download PDF
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-12 pt-8 border-t border-gray-100">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2 border border-gray-300 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors font-medium"
                  >
                    Previous
                  </button>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`px-4 py-2 border rounded-xl font-medium transition-colors ${
                          currentPage === page
                            ? "bg-blue-600 text-white border-blue-600 shadow-lg"
                            : "border-gray-300 hover:bg-gray-50 hover:border-blue-300"
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 border border-gray-300 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors font-medium"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
