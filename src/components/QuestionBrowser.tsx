"use client";
import { useEffect, useState } from "react";
import { supabase } from "../supabase";

export default function QuestionBrowser() {
  const [universities, setUniversities] = useState<any[]>([]);
  const [courses, setCourses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [papers, setPapers] = useState<any[]>([]);
  const [filters, setFilters] = useState({ university: "", course: "", subject: "", semester: "", year: "" });
  const [search, setSearch] = useState("");

  useEffect(() => {
    supabase.from("universities").select("*").then(({ data }) => setUniversities(data || []));
  }, []);

  useEffect(() => {
    if (filters.university) {
      supabase.from("courses").select("*").eq("university_id", filters.university).then(({ data }) => setCourses(data || []));
    } else setCourses([]);
  }, [filters.university]);

  useEffect(() => {
    if (filters.course) {
      supabase.from("subjects").select("*").eq("course_id", filters.course).then(({ data }) => setSubjects(data || []));
    } else setSubjects([]);
  }, [filters.course]);

  useEffect(() => {
    let query = supabase.from("question_papers").select("*, subjects(name, course_id)");
    if (filters.subject) query = query.eq("subject_id", filters.subject);
    if (filters.year) query = query.eq("year", filters.year);
    if (filters.semester) query = query.eq("semester", filters.semester);
    if (search) query = query.textSearch("file_url", search, { type: "websearch" });
    query.then(({ data }) => setPapers(data || []));
  }, [filters.subject, filters.year, filters.semester, search]);

  return (
    <div className="max-w-3xl mx-auto mt-10 p-6 bg-white rounded shadow">
      <h2 className="text-xl font-bold mb-4">Browse Question Papers</h2>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-2 mb-6">
        <select className="border p-2 rounded" value={filters.university} onChange={e => setFilters(f => ({ ...f, university: e.target.value, course: "", subject: "" }))}>
          <option value="">University</option>
          {universities.map(u => <option key={u.id} value={u.id}>{u.name}</option>)}
        </select>
        <select className="border p-2 rounded" value={filters.course} onChange={e => setFilters(f => ({ ...f, course: e.target.value, subject: "" }))} disabled={!filters.university}>
          <option value="">Course</option>
          {courses.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
        </select>
        <select className="border p-2 rounded" value={filters.subject} onChange={e => setFilters(f => ({ ...f, subject: e.target.value }))} disabled={!filters.course}>
          <option value="">Subject</option>
          {subjects.map(s => <option key={s.id} value={s.id}>{s.name}</option>)}
        </select>
        <input className="border p-2 rounded" type="number" placeholder="Semester" value={filters.semester} onChange={e => setFilters(f => ({ ...f, semester: e.target.value }))} />
        <input className="border p-2 rounded" type="number" placeholder="Year" value={filters.year} onChange={e => setFilters(f => ({ ...f, year: e.target.value }))} />
      </div>
      <input
        className="border p-2 rounded mb-4 w-full"
        type="text"
        placeholder="Search by keyword (e.g. year, subject, etc.)"
        value={search}
        onChange={e => setSearch(e.target.value)}
      />
      <div className="space-y-4">
        {papers.length === 0 && <div className="text-gray-500">No question papers found.</div>}
        {papers.map(paper => (
          <div key={paper.id} className="p-4 border rounded flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <div className="font-semibold">{paper.subjects?.name || "Unknown Subject"}</div>
              <div className="text-sm text-gray-600">Year: {paper.year} | Semester: {paper.semester}</div>
            </div>
            <a href={paper.file_url} target="_blank" rel="noopener noreferrer" className="mt-2 md:mt-0 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Download</a>
          </div>
        ))}
      </div>
    </div>
  );
}
