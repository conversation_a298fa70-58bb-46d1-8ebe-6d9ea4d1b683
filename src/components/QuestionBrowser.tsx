"use client";
import { useEffect, useState, useCallback } from "react";
import { supabase } from "../supabase";

interface University {
  id: string;
  name: string;
  short_name?: string;
}

interface Course {
  id: string;
  name: string;
  code?: string;
}

interface Subject {
  id: string;
  name: string;
  code?: string;
  semester: number;
}

interface QuestionPaper {
  id: string;
  title: string;
  year: number;
  semester: number;
  exam_type: string;
  file_url: string;
  file_name: string;
  file_size?: number;
  keywords?: string[];
  description?: string;
  download_count: number;
  created_at: string;
  university: { name: string };
  course: { name: string; code?: string };
  subject: { name: string; code?: string };
}

interface Filters {
  university: string;
  course: string;
  subject: string;
  semester: string;
  year: string;
  examType: string;
}

export default function QuestionBrowser() {
  const [universities, setUniversities] = useState<University[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [papers, setPapers] = useState<QuestionPaper[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<Filters>({
    university: "",
    course: "",
    subject: "",
    semester: "",
    year: "",
    examType: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const itemsPerPage = 10;

  // Load universities on component mount
  useEffect(() => {
    loadUniversities();
  }, []);

  // Load courses when university changes
  useEffect(() => {
    if (filters.university) {
      loadCourses(filters.university);
      setFilters(prev => ({ ...prev, course: "", subject: "" }));
    } else {
      setCourses([]);
      setSubjects([]);
    }
  }, [filters.university]);

  // Load subjects when course changes
  useEffect(() => {
    if (filters.course) {
      loadSubjects(filters.course);
      setFilters(prev => ({ ...prev, subject: "" }));
    } else {
      setSubjects([]);
    }
  }, [filters.course]);

  // Search papers when filters or search term changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchPapers();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [filters, searchTerm, currentPage]);

  const loadUniversities = async () => {
    const { data, error } = await supabase
      .from("universities")
      .select("id, name, short_name")
      .order("name");

    if (error) {
      console.error("Error loading universities:", error);
    } else {
      setUniversities(data || []);
    }
  };

  const loadCourses = async (universityId: string) => {
    const { data, error } = await supabase
      .from("courses")
      .select("id, name, code")
      .eq("university_id", universityId)
      .order("name");

    if (error) {
      console.error("Error loading courses:", error);
    } else {
      setCourses(data || []);
    }
  };

  const loadSubjects = async (courseId: string) => {
    const { data, error } = await supabase
      .from("subjects")
      .select("id, name, code, semester")
      .eq("course_id", courseId)
      .order("semester, name");

    if (error) {
      console.error("Error loading subjects:", error);
    } else {
      setSubjects(data || []);
    }
  };

  const searchPapers = async () => {
    setLoading(true);
    try {
      // Use the search function we created in the schema
      const { data, error } = await supabase.rpc('search_question_papers', {
        search_term: searchTerm,
        university_filter: filters.university || null,
        course_filter: filters.course || null,
        subject_filter: filters.subject || null,
        year_filter: filters.year ? parseInt(filters.year) : null,
        semester_filter: filters.semester ? parseInt(filters.semester) : null,
        exam_type_filter: filters.examType || null
      });

      if (error) {
        console.error("Error searching papers:", error);
        setPapers([]);
      } else {
        // Transform the data to match our interface
        const transformedPapers = (data || []).map((paper: any) => ({
          id: paper.id,
          title: paper.title,
          year: paper.year,
          semester: paper.semester,
          exam_type: paper.exam_type,
          file_url: paper.file_url,
          file_name: paper.file_name,
          download_count: paper.download_count,
          created_at: paper.created_at,
          university: { name: paper.university_name },
          course: { name: paper.course_name },
          subject: { name: paper.subject_name }
        }));

        // Apply pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        setPapers(transformedPapers.slice(startIndex, endIndex));
        setTotalCount(transformedPapers.length);
      }
    } catch (error) {
      console.error("Error in search:", error);
      setPapers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (paperId: string, fileUrl: string) => {
    try {
      // Increment download count
      await supabase.rpc('increment_download_count', { paper_id: paperId });

      // Open file in new tab
      window.open(fileUrl, '_blank');

      // Update local state
      setPapers(prev => prev.map(paper =>
        paper.id === paperId
          ? { ...paper, download_count: paper.download_count + 1 }
          : paper
      ));
    } catch (error) {
      console.error("Error tracking download:", error);
      // Still allow download even if tracking fails
      window.open(fileUrl, '_blank');
    }
  };

  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setFilters({
      university: "",
      course: "",
      subject: "",
      semester: "",
      year: "",
      examType: ""
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  const examTypes = [
    { value: "", label: "All Exam Types" },
    { value: "mid_term", label: "Mid Term" },
    { value: "end_term", label: "End Term" },
    { value: "quiz", label: "Quiz" },
    { value: "assignment", label: "Assignment" },
    { value: "practical", label: "Practical" },
    { value: "other", label: "Other" }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 25 }, (_, i) => currentYear - i);
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  return (
    <div className="max-w-6xl mx-auto mt-10 p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl font-bold text-gray-800">Browse Question Papers</h2>
        <button
          onClick={clearFilters}
          className="px-4 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Clear Filters
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search by title, subject, university, or keywords..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <select
          value={filters.university}
          onChange={(e) => handleFilterChange("university", e.target.value)}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Universities</option>
          {universities.map((university) => (
            <option key={university.id} value={university.id}>
              {university.short_name || university.name}
            </option>
          ))}
        </select>

        <select
          value={filters.course}
          onChange={(e) => handleFilterChange("course", e.target.value)}
          disabled={!filters.university}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        >
          <option value="">All Courses</option>
          {courses.map((course) => (
            <option key={course.id} value={course.id}>
              {course.name} {course.code && `(${course.code})`}
            </option>
          ))}
        </select>

        <select
          value={filters.subject}
          onChange={(e) => handleFilterChange("subject", e.target.value)}
          disabled={!filters.course}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        >
          <option value="">All Subjects</option>
          {subjects.map((subject) => (
            <option key={subject.id} value={subject.id}>
              {subject.name} {subject.code && `(${subject.code})`}
            </option>
          ))}
        </select>

        <select
          value={filters.year}
          onChange={(e) => handleFilterChange("year", e.target.value)}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Years</option>
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>

        <select
          value={filters.semester}
          onChange={(e) => handleFilterChange("semester", e.target.value)}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Semesters</option>
          {Array.from({ length: 8 }, (_, i) => i + 1).map((sem) => (
            <option key={sem} value={sem}>
              Semester {sem}
            </option>
          ))}
        </select>

        <select
          value={filters.examType}
          onChange={(e) => handleFilterChange("examType", e.target.value)}
          className="p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {examTypes.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Searching...</span>
        </div>
      )}

      {/* Results */}
      {!loading && (
        <>
          <div className="mb-4 text-sm text-gray-600">
            Found {totalCount} question papers
            {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
          </div>

          <div className="space-y-4">
            {papers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="text-lg mb-2">No question papers found</div>
                <div className="text-sm">Try adjusting your search criteria or filters</div>
              </div>
            ) : (
              papers.map((paper) => (
                <div
                  key={paper.id}
                  className="p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        {paper.title}
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">University:</span> {paper.university.name}
                        </div>
                        <div>
                          <span className="font-medium">Course:</span> {paper.course.name}
                        </div>
                        <div>
                          <span className="font-medium">Subject:</span> {paper.subject.name}
                        </div>
                        <div>
                          <span className="font-medium">Year:</span> {paper.year} |
                          <span className="font-medium"> Semester:</span> {paper.semester}
                        </div>
                        <div>
                          <span className="font-medium">Exam Type:</span>{" "}
                          {paper.exam_type.replace("_", " ").replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                        <div>
                          <span className="font-medium">Downloads:</span> {paper.download_count}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        Uploaded on {new Date(paper.created_at).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="mt-4 lg:mt-0 lg:ml-6">
                      <button
                        onClick={() => handleDownload(paper.id, paper.file_url)}
                        className="w-full lg:w-auto px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download PDF
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-8">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-2 border rounded ${
                      currentPage === page
                        ? "bg-blue-600 text-white border-blue-600"
                        : "border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    {page}
                  </button>
                );
              })}

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
