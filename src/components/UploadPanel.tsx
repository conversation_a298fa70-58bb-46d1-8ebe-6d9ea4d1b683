"use client";
import { useState, useEffect } from "react";
import { supabase } from "../supabase";

interface University {
  id: string;
  name: string;
}

interface Course {
  id: string;
  name: string;
  code: string;
}

interface Subject {
  id: string;
  name: string;
  code: string;
  semester: number;
}

export default function UploadPanel() {
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState("");
  const [universityId, setUniversityId] = useState("");
  const [courseId, setCourseId] = useState("");
  const [subjectId, setSubjectId] = useState("");
  const [year, setYear] = useState("");
  const [semester, setSemester] = useState("");
  const [examType, setExamType] = useState("end_term");
  const [keywords, setKeywords] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "info">("info");

  const [universities, setUniversities] = useState<University[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);

  // Load universities on component mount
  useEffect(() => {
    loadUniversities();
  }, []);

  // Load courses when university changes
  useEffect(() => {
    if (universityId) {
      loadCourses(universityId);
      setCourseId("");
      setSubjectId("");
    }
  }, [universityId]);

  // Load subjects when course changes
  useEffect(() => {
    if (courseId) {
      loadSubjects(courseId);
      setSubjectId("");
    }
  }, [courseId]);

  const loadUniversities = async () => {
    const { data, error } = await supabase
      .from("universities")
      .select("id, name")
      .order("name");

    if (error) {
      console.error("Error loading universities:", error);
    } else {
      setUniversities(data || []);
    }
  };

  const loadCourses = async (universityId: string) => {
    const { data, error } = await supabase
      .from("courses")
      .select("id, name, code")
      .eq("university_id", universityId)
      .order("name");

    if (error) {
      console.error("Error loading courses:", error);
    } else {
      setCourses(data || []);
    }
  };

  const loadSubjects = async (courseId: string) => {
    const { data, error } = await supabase
      .from("subjects")
      .select("id, name, code, semester")
      .eq("course_id", courseId)
      .order("semester, name");

    if (error) {
      console.error("Error loading subjects:", error);
    } else {
      setSubjects(data || []);
    }
  };

  const validateFile = (file: File): string | null => {
    if (file.type !== "application/pdf") {
      return "Only PDF files are allowed.";
    }
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      return "File size must be less than 50MB.";
    }
    return null;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    if (selectedFile) {
      const error = validateFile(selectedFile);
      if (error) {
        setMessage(error);
        setMessageType("error");
        setFile(null);
        return;
      }
      setFile(selectedFile);
      if (!title) {
        // Auto-generate title from filename
        const nameWithoutExt = selectedFile.name.replace(/\.[^/.]+$/, "");
        setTitle(nameWithoutExt);
      }
    } else {
      setFile(null);
    }
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file || !title || !universityId || !courseId || !subjectId || !year || !semester) {
      setMessage("All required fields must be filled.");
      setMessageType("error");
      return;
    }

    setLoading(true);
    setUploadProgress(0);
    setMessage("Uploading file...");
    setMessageType("info");

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("You must be logged in to upload files.");
      }

      // Create unique filename
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop();
      const fileName = `${year}_${semester}_${examType}_${timestamp}.${fileExtension}`;
      const filePath = `question-papers/${universityId}/${courseId}/${subjectId}/${fileName}`;

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("question-papers")
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      setUploadProgress(50);
      setMessage("File uploaded, saving metadata...");

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from("question-papers")
        .getPublicUrl(filePath);

      // Parse keywords
      const keywordArray = keywords
        .split(',')
        .map(k => k.trim())
        .filter(k => k.length > 0);

      // Insert question paper record
      const { error: insertError } = await supabase
        .from("question_papers")
        .insert([{
          title,
          subject_id: subjectId,
          university_id: universityId,
          course_id: courseId,
          year: parseInt(year),
          semester: parseInt(semester),
          exam_type: examType,
          file_url: publicUrl,
          file_name: fileName,
          file_size: file.size,
          keywords: keywordArray,
          description: description || null,
          uploaded_by: user.id
        }]);

      if (insertError) throw insertError;

      setUploadProgress(100);
      setMessage("Upload successful! Your file is pending approval.");
      setMessageType("success");

      // Reset form
      setFile(null);
      setTitle("");
      setYear("");
      setSemester("");
      setExamType("end_term");
      setKeywords("");
      setDescription("");

      // Reset file input
      const fileInput = document.getElementById("file-input") as HTMLInputElement;
      if (fileInput) fileInput.value = "";

    } catch (err: any) {
      setMessage(err.message || "An error occurred during upload.");
      setMessageType("error");
      setUploadProgress(0);
    } finally {
      setLoading(false);
    }
  };

  const examTypes = [
    { value: "mid_term", label: "Mid Term" },
    { value: "end_term", label: "End Term" },
    { value: "quiz", label: "Quiz" },
    { value: "assignment", label: "Assignment" },
    { value: "practical", label: "Practical" },
    { value: "other", label: "Other" }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 25 }, (_, i) => currentYear - i);

  return (
    <div className="max-w-2xl mx-auto mt-10 p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">Upload Question Paper</h2>

      <form onSubmit={handleUpload} className="space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter question paper title"
            required
          />
        </div>

        {/* University Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            University *
          </label>
          <select
            value={universityId}
            onChange={(e) => setUniversityId(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select University</option>
            {universities.map((university) => (
              <option key={university.id} value={university.id}>
                {university.name}
              </option>
            ))}
          </select>
        </div>

        {/* Course Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Course *
          </label>
          <select
            value={courseId}
            onChange={(e) => setCourseId(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
            disabled={!universityId}
          >
            <option value="">Select Course</option>
            {courses.map((course) => (
              <option key={course.id} value={course.id}>
                {course.name} {course.code && `(${course.code})`}
              </option>
            ))}
          </select>
        </div>

        {/* Subject Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Subject *
          </label>
          <select
            value={subjectId}
            onChange={(e) => setSubjectId(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
            disabled={!courseId}
          >
            <option value="">Select Subject</option>
            {subjects.map((subject) => (
              <option key={subject.id} value={subject.id}>
                {subject.name} {subject.code && `(${subject.code})`} - Sem {subject.semester}
              </option>
            ))}
          </select>
        </div>

        {/* Year and Semester */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Year *
            </label>
            <select
              value={year}
              onChange={(e) => setYear(e.target.value)}
              className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select Year</option>
              {years.map((y) => (
                <option key={y} value={y}>
                  {y}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Semester *
            </label>
            <select
              value={semester}
              onChange={(e) => setSemester(e.target.value)}
              className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select Semester</option>
              {Array.from({ length: 8 }, (_, i) => i + 1).map((sem) => (
                <option key={sem} value={sem}>
                  Semester {sem}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Exam Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Exam Type *
          </label>
          <select
            value={examType}
            onChange={(e) => setExamType(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            {examTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Keywords */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Keywords
          </label>
          <input
            type="text"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter keywords separated by commas"
          />
          <p className="text-sm text-gray-500 mt-1">
            Add keywords to help students find this paper (e.g., algorithms, data structures, calculus)
          </p>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
            placeholder="Optional description or notes about this question paper"
          />
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            PDF File *
          </label>
          <input
            id="file-input"
            type="file"
            accept="application/pdf"
            onChange={handleFileChange}
            className="w-full border border-gray-300 p-3 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
          <p className="text-sm text-gray-500 mt-1">
            Maximum file size: 50MB. Only PDF files are allowed.
          </p>
          {file && (
            <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
              <strong>Selected:</strong> {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {loading && uploadProgress > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
            loading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
          } text-white`}
          disabled={loading}
        >
          {loading ? "Uploading..." : "Upload Question Paper"}
        </button>

        {/* Message */}
        {message && (
          <div
            className={`p-3 rounded-md text-sm ${
              messageType === "success"
                ? "bg-green-50 text-green-800 border border-green-200"
                : messageType === "error"
                ? "bg-red-50 text-red-800 border border-red-200"
                : "bg-blue-50 text-blue-800 border border-blue-200"
            }`}
          >
            {message}
          </div>
        )}
      </form>
    </div>
  );
}
