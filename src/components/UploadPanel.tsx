"use client";
import { useState } from "react";
import { supabase } from "../supabase";

export default function UploadPanel() {
  const [file, setFile] = useState<File | null>(null);
  const [subjectId, setSubjectId] = useState("");
  const [year, setYear] = useState("");
  const [semester, setSemester] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !subjectId || !year || !semester) {
      setMessage("All fields are required.");
      return;
    }
    setLoading(true);
    setMessage("");
    try {
      const filePath = `question-papers/${subjectId}_${year}_${semester}_${file.name}`;
      const { data, error: uploadError } = await supabase.storage.from("question-papers").upload(filePath, file);
      if (uploadError) throw uploadError;
      const { data: { publicUrl } } = supabase.storage.from("question-papers").getPublicUrl(filePath);
      const { error: insertError } = await supabase.from("question_papers").insert([
        {
          subject_id: subjectId,
          year: parseInt(year),
          semester: parseInt(semester),
          file_url: publicUrl,
          // uploaded_by: user id (set on server or via RLS)
        },
      ]);
      if (insertError) throw insertError;
      setMessage("Upload successful!");
      setFile(null);
      setYear("");
      setSemester("");
      setSubjectId("");
    } catch (err: any) {
      setMessage(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleUpload} className="max-w-md mx-auto mt-10 p-6 bg-white rounded shadow space-y-4">
      <h2 className="text-xl font-bold text-center">Upload Question Paper</h2>
      <input
        type="text"
        placeholder="Subject ID"
        value={subjectId}
        onChange={e => setSubjectId(e.target.value)}
        className="w-full border p-2 rounded"
        required
      />
      <input
        type="number"
        placeholder="Year"
        value={year}
        onChange={e => setYear(e.target.value)}
        className="w-full border p-2 rounded"
        required
      />
      <input
        type="number"
        placeholder="Semester"
        value={semester}
        onChange={e => setSemester(e.target.value)}
        className="w-full border p-2 rounded"
        required
      />
      <input
        type="file"
        accept="application/pdf"
        onChange={e => setFile(e.target.files?.[0] || null)}
        className="w-full border p-2 rounded"
        required
      />
      <button
        type="submit"
        className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 disabled:opacity-50"
        disabled={loading}
      >
        {loading ? "Uploading..." : "Upload"}
      </button>
      {message && <div className="text-center text-sm mt-2">{message}</div>}
    </form>
  );
}
