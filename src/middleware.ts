import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

// Define route protection rules
const routeProtection = {
  // Routes that require authentication
  protected: ['/dashboard', '/upload', '/profile'],

  // Routes that require specific roles
  roleRequired: {
    '/upload': ['admin', 'uploader'],
    '/dashboard': ['admin', 'uploader'],
    '/admin': ['admin']
  },

  // Routes that redirect authenticated users
  authRedirect: ['/auth/login', '/auth/signup'],

  // Public routes (no authentication required)
  public: ['/', '/browse', '/about', '/contact']
};

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });
  const url = req.nextUrl.pathname;

  try {
    // Get user session
    const {
      data: { user },
      error: authError
    } = await supabase.auth.getUser();

    if (authError) {
      console.error('Auth error in middleware:', authError);
    }

    // Handle auth redirect routes (login/signup pages)
    if (routeProtection.authRedirect.some(route => url.startsWith(route))) {
      if (user) {
        // User is already authenticated, redirect to appropriate page
        try {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single();

          if (profile?.role === 'admin' || profile?.role === 'uploader') {
            return NextResponse.redirect(new URL('/dashboard', req.url));
          } else {
            return NextResponse.redirect(new URL('/browse', req.url));
          }
        } catch (error) {
          console.error('Error fetching profile in middleware:', error);
          return NextResponse.redirect(new URL('/browse', req.url));
        }
      }
      // User not authenticated, allow access to auth pages
      return res;
    }

    // Handle protected routes
    if (routeProtection.protected.some(route => url.startsWith(route))) {
      if (!user) {
        // User not authenticated, redirect to login
        const redirectUrl = new URL('/auth/login', req.url);
        redirectUrl.searchParams.set('redirect', url);
        return NextResponse.redirect(redirectUrl);
      }

      // Check role-based access
      const requiredRoles = Object.entries(routeProtection.roleRequired)
        .find(([route]) => url.startsWith(route))?.[1];

      if (requiredRoles) {
        try {
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('role, is_active')
            .eq('id', user.id)
            .single();

          if (profileError) {
            console.error('Error fetching profile:', profileError);
            return NextResponse.redirect(new URL('/auth/login', req.url));
          }

          if (!profile) {
            console.error('No profile found for user');
            return NextResponse.redirect(new URL('/auth/login', req.url));
          }

          if (!profile.is_active) {
            return NextResponse.redirect(new URL('/account-suspended', req.url));
          }

          if (!requiredRoles.includes(profile.role)) {
            // User doesn't have required role
            return NextResponse.redirect(new URL('/unauthorized', req.url));
          }
        } catch (error) {
          console.error('Error in role check:', error);
          return NextResponse.redirect(new URL('/auth/login', req.url));
        }
      }
    }

    // Handle root redirect
    if (url === '/') {
      if (user) {
        try {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single();

          if (profile?.role === 'admin' || profile?.role === 'uploader') {
            return NextResponse.redirect(new URL('/dashboard', req.url));
          } else {
            return NextResponse.redirect(new URL('/browse', req.url));
          }
        } catch (error) {
          console.error('Error in root redirect:', error);
          return NextResponse.redirect(new URL('/browse', req.url));
        }
      } else {
        return NextResponse.redirect(new URL('/browse', req.url));
      }
    }

    return res;

  } catch (error) {
    console.error('Middleware error:', error);
    // On error, allow the request to proceed but log the error
    return res;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
