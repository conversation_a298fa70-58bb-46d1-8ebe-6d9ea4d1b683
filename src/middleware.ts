import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

const protectedRoutes = [
  '/dashboard',
  '/upload',
];

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const url = req.nextUrl.pathname;

  if (protectedRoutes.some(route => url.startsWith(route))) {
    if (!user) {
      return NextResponse.redirect(new URL('/login', req.url));
    }
    // Optionally, fetch user profile and check role for /upload
    if (url.startsWith('/upload')) {
      const { data: profile } = await supabase.from('profiles').select('role').eq('id', user.id).single();
      if (!profile || !['admin', 'uploader'].includes(profile.role)) {
        return NextResponse.redirect(new URL('/', req.url));
      }
    }
  }
  return res;
}

export const config = {
  matcher: ['/dashboard', '/upload'],
};
