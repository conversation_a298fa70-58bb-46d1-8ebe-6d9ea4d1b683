-- Enable RLS on all tables
alter table universities enable row level security;
alter table courses enable row level security;
alter table subjects enable row level security;
alter table question_papers enable row level security;
alter table profiles enable row level security;

-- Universities policies
create policy "Allow everyone to read universities" on universities
  for select using (true);

create policy "Allow admin to manage universities" on universities
  for all using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

-- Courses policies
create policy "Allow everyone to read courses" on courses
  for select using (true);

create policy "Allow admin to manage courses" on courses
  for all using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

-- Subjects policies
create policy "Allow everyone to read subjects" on subjects
  for select using (true);

create policy "Allow admin to manage subjects" on subjects
  for all using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

-- Question papers policies
create policy "Allow everyone to read approved question papers" on question_papers
  for select using (is_approved = true);

create policy "Allow admin and uploader to read all question papers" on question_papers
  for select using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

create policy "Allow admin and uploader to insert question papers" on question_papers
  for insert with check (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

create policy "Allow admin and uploader to update their own question papers" on question_papers
  for update using (
    uploaded_by = auth.uid() and
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

create policy "Allow admin to update any question papers" on question_papers
  for update using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

create policy "Allow admin and uploader to delete their own question papers" on question_papers
  for delete using (
    uploaded_by = auth.uid() and
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

create policy "Allow admin to delete any question papers" on question_papers
  for delete using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

-- Profiles policies
create policy "Allow users to read their own profile" on profiles
  for select using (auth.uid() = id);

create policy "Allow admin to read all profiles" on profiles
  for select using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

create policy "Allow users to insert their own profile" on profiles
  for insert with check (auth.uid() = id);

create policy "Allow users to update their own profile" on profiles
  for update using (auth.uid() = id)
  with check (
    auth.uid() = id and
    -- Prevent users from changing their own role unless they're admin
    (role = (select role from profiles where id = auth.uid()) or
     exists (select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'))
  );

create policy "Allow admin to update any profile" on profiles
  for update using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );
