-- Enable RLS on tables
alter table question_papers enable row level security;
alter table profiles enable row level security;

-- Only admin/uploader can insert into question_papers
create policy "Allow admin and uploader to insert question papers" on question_papers
  for insert using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

-- Any student can read from question_papers
create policy "Allow students to read question papers" on question_papers
  for select using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'student'
    ) or
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('admin', 'uploader')
    )
  );

-- Profiles can only be updated by the owner
create policy "Allow users to update their own profile" on profiles
  for update using (auth.uid() = id);
