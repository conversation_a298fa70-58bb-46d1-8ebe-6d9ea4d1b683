-- Supabase SQL schema for PYQFinder

-- Table: universities
create table universities (
  id uuid primary key default gen_random_uuid(),
  name text not null unique,
  short_name text,
  location text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Table: courses
create table courses (
  id uuid primary key default gen_random_uuid(),
  university_id uuid references universities(id) on delete cascade,
  name text not null,
  code text,
  duration_years int default 4,
  degree_type text check (degree_type in ('undergraduate', 'postgraduate', 'diploma')) default 'undergraduate',
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique (university_id, name)
);

-- Table: subjects
create table subjects (
  id uuid primary key default gen_random_uuid(),
  course_id uuid references courses(id) on delete cascade,
  name text not null,
  code text,
  semester int not null check (semester >= 1 and semester <= 12),
  credits int default 3,
  is_elective boolean default false,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique (course_id, name, semester)
);

-- Table: profiles
create table profiles (
  id uuid primary key, -- matches auth.users.id
  full_name text,
  email text unique,
  role text check (role in ('student', 'admin', 'uploader')) not null default 'student',
  university_id uuid references universities(id),
  course_id uuid references courses(id),
  current_semester int,
  phone text,
  is_active boolean default true,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Table: question_papers
create table question_papers (
  id uuid primary key default gen_random_uuid(),
  title text not null,
  subject_id uuid references subjects(id) on delete cascade,
  university_id uuid references universities(id) on delete cascade,
  course_id uuid references courses(id) on delete cascade,
  year int not null check (year >= 2000 and year <= extract(year from now()) + 1),
  semester int not null check (semester >= 1 and semester <= 12),
  exam_type text check (exam_type in ('mid_term', 'end_term', 'quiz', 'assignment', 'practical', 'other')) default 'end_term',
  file_url text not null,
  file_name text not null,
  file_size bigint, -- in bytes
  keywords text[], -- array of keywords for search
  description text,
  download_count int default 0,
  is_approved boolean default false,
  uploaded_by uuid references profiles(id),
  approved_by uuid references profiles(id),
  approved_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique (subject_id, year, semester, exam_type)
);

-- Create indexes for better performance
create index idx_courses_university_id on courses(university_id);
create index idx_subjects_course_id on subjects(course_id);
create index idx_subjects_semester on subjects(semester);
create index idx_profiles_university_id on profiles(university_id);
create index idx_profiles_role on profiles(role);
create index idx_question_papers_subject_id on question_papers(subject_id);
create index idx_question_papers_university_id on question_papers(university_id);
create index idx_question_papers_course_id on question_papers(course_id);
create index idx_question_papers_year on question_papers(year);
create index idx_question_papers_semester on question_papers(semester);
create index idx_question_papers_exam_type on question_papers(exam_type);
create index idx_question_papers_is_approved on question_papers(is_approved);
create index idx_question_papers_keywords on question_papers using gin(keywords);
create index idx_question_papers_created_at on question_papers(created_at);

-- Function to update updated_at timestamp
create or replace function update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Create triggers for updated_at
create trigger update_universities_updated_at before update on universities
  for each row execute function update_updated_at_column();

create trigger update_courses_updated_at before update on courses
  for each row execute function update_updated_at_column();

create trigger update_subjects_updated_at before update on subjects
  for each row execute function update_updated_at_column();

create trigger update_profiles_updated_at before update on profiles
  for each row execute function update_updated_at_column();

create trigger update_question_papers_updated_at before update on question_papers
  for each row execute function update_updated_at_column();

-- Function to increment download count
create or replace function increment_download_count(paper_id uuid)
returns void as $$
begin
  update question_papers
  set download_count = download_count + 1,
      updated_at = now()
  where id = paper_id;
end;
$$ language plpgsql security definer;

-- Function for full-text search
create or replace function search_question_papers(
  search_term text default '',
  university_filter uuid default null,
  course_filter uuid default null,
  subject_filter uuid default null,
  year_filter int default null,
  semester_filter int default null,
  exam_type_filter text default null
)
returns table (
  id uuid,
  title text,
  university_name text,
  course_name text,
  subject_name text,
  year int,
  semester int,
  exam_type text,
  file_url text,
  file_name text,
  download_count int,
  created_at timestamptz
) as $$
begin
  return query
  select
    qp.id,
    qp.title,
    u.name as university_name,
    c.name as course_name,
    s.name as subject_name,
    qp.year,
    qp.semester,
    qp.exam_type,
    qp.file_url,
    qp.file_name,
    qp.download_count,
    qp.created_at
  from question_papers qp
  join subjects s on qp.subject_id = s.id
  join courses c on qp.course_id = c.id
  join universities u on qp.university_id = u.id
  where
    qp.is_approved = true
    and (search_term = '' or
         qp.title ilike '%' || search_term || '%' or
         s.name ilike '%' || search_term || '%' or
         c.name ilike '%' || search_term || '%' or
         u.name ilike '%' || search_term || '%' or
         search_term = any(qp.keywords))
    and (university_filter is null or qp.university_id = university_filter)
    and (course_filter is null or qp.course_id = course_filter)
    and (subject_filter is null or qp.subject_id = subject_filter)
    and (year_filter is null or qp.year = year_filter)
    and (semester_filter is null or qp.semester = semester_filter)
    and (exam_type_filter is null or qp.exam_type = exam_type_filter)
  order by qp.created_at desc;
end;
$$ language plpgsql security definer;

-- Table: download_logs (for analytics)
create table download_logs (
  id uuid primary key default gen_random_uuid(),
  paper_id uuid references question_papers(id) on delete cascade,
  user_id uuid references profiles(id) on delete set null,
  ip_address inet,
  user_agent text,
  downloaded_at timestamptz default now()
);

-- Index for download logs
create index idx_download_logs_paper_id on download_logs(paper_id);
create index idx_download_logs_user_id on download_logs(user_id);
create index idx_download_logs_downloaded_at on download_logs(downloaded_at);

-- RLS for download_logs
alter table download_logs enable row level security;

create policy "Allow admin to read all download logs" on download_logs
  for select using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'admin'
    )
  );

create policy "Allow users to read their own download logs" on download_logs
  for select using (user_id = auth.uid());

create policy "Allow anyone to insert download logs" on download_logs
  for insert with check (true);
