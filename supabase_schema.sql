-- Supabase SQL schema for PYQFinder

-- Table: universities
create table universities (
  id uuid primary key default gen_random_uuid(),
  name text not null unique
);

-- Table: courses
create table courses (
  id uuid primary key default gen_random_uuid(),
  university_id uuid references universities(id) on delete cascade,
  name text not null,
  code text,
  unique (university_id, name)
);

-- Table: subjects
create table subjects (
  id uuid primary key default gen_random_uuid(),
  course_id uuid references courses(id) on delete cascade,
  name text not null,
  code text,
  semester int,
  unique (course_id, name, semester)
);

-- Table: profiles
create table profiles (
  id uuid primary key, -- matches auth.users.id
  full_name text,
  email text unique,
  role text check (role in ('student', 'admin', 'uploader')) not null default 'student',
  university_id uuid references universities(id)
);

-- Table: question_papers
create table question_papers (
  id uuid primary key default gen_random_uuid(),
  subject_id uuid references subjects(id) on delete cascade,
  year int not null,
  semester int not null,
  file_url text not null,
  uploaded_by uuid references profiles(id),
  created_at timestamptz default now(),
  unique (subject_id, year, semester)
);
